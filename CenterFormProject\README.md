# مشروع الفورم المتمركز - Center Form Project

## 🎯 وصف المشروع

هذا مشروع تعليمي بلغة C# يوضح كيفية إنشاء نافذة Windows Forms تظل دائماً في منتصف الشاشة، حتى لو حاول المستخدم تحريكها أو تغيير حجمها.

## 📐 المعادلة العلمية المستخدمة

```
الإحداثي الأفقي (X) = (عرض الشاشة - عرض النافذة) ÷ 2
الإحداثي الرأسي (Y) = (طول الشاشة - طول النافذة) ÷ 2
```

## 🚀 كيفية تشغيل المشروع

### المتطلبات:
- .NET 8.0 SDK أو أحدث
- Windows Operating System

### خطوات التشغيل:

1. **فتح موجه الأوامر:**
   ```bash
   cd CenterFormProject
   ```

2. **بناء المشروع:**
   ```bash
   dotnet build
   ```

3. **تشغيل المشروع:**
   ```bash
   dotnet run
   ```

## 🎮 ميزات المشروع

- ✅ التمركز التلقائي عند بدء التشغيل
- ✅ إعادة التمركز عند تغيير حجم النافذة
- ✅ إعادة التمركز عند محاولة تحريك النافذة
- ✅ منع تكبير النافذة
- ✅ واجهة مستخدم توضيحية
- ✅ عرض معلومات الحسابات في الوقت الفعلي

## 📁 ملفات المشروع

| الملف | الوصف |
|-------|--------|
| `Form1.cs` | الملف الرئيسي الذي يحتوي على منطق التمركز |
| `Form1.Designer.cs` | ملف تصميم النافذة |
| `Program.cs` | نقطة بداية البرنامج |
| `شرح_المشروع.txt` | شرح مفصل للمشروع |
| `الكود_الكامل_مع_الشرح.txt` | الكود الكامل مع شرح كل سطر |

## 🧠 المفاهيم البرمجية المستخدمة

### الأحداث (Events):
- `Resize`: يتم استدعاؤه عند تغيير حجم النافذة
- `LocationChanged`: يتم استدعاؤه عند تحريك النافذة

### خصائص الشاشة:
- `Screen.PrimaryScreen.Bounds.Width`: عرض الشاشة
- `Screen.PrimaryScreen.Bounds.Height`: طول الشاشة

### خصائص النافذة:
- `Width`, `Height`: أبعاد النافذة
- `Location`: موقع النافذة
- `FormBorderStyle`: نوع حدود النافذة

## 🔧 كيفية اختبار المشروع

1. **تشغيل البرنامج** - ستظهر النافذة في منتصف الشاشة
2. **محاولة تحريك النافذة** - ستعود للمنتصف فوراً
3. **الضغط على زر "إعادة التمركز يدوياً"** - لإعادة التمركز
4. **ملاحظة المعلومات المعروضة** - في النافذة وشريط العنوان

## 💡 الفوائد التعليمية

- فهم نظام الإحداثيات في Windows
- تطبيق المعادلات الرياضية في البرمجة
- التعامل مع أحداث النوافذ
- إنشاء واجهات مستخدم تفاعلية
- استخدام خصائص الشاشة والنوافذ

## 🚀 إمكانيات التطوير المستقبلية

- إضافة خيار لتمركز النافذة في شاشات متعددة
- حفظ واستعادة موقع النافذة المفضل
- إضافة تأثيرات بصرية عند التمركز
- إنشاء إعدادات قابلة للتخصيص

## 📞 ملاحظات

هذا المشروع مثال تعليمي ممتاز لفهم:
- كيفية التحكم في موقع النوافذ برمجياً
- استخدام الأحداث للاستجابة لتفاعل المستخدم
- تطبيق المعادلات الرياضية في حل المشاكل البرمجية

---

**تم إنشاء هذا المشروع كمثال تعليمي شامل يجمع بين النظرية والتطبيق العملي.**
