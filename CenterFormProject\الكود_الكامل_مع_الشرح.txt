===============================================
الكود الكامل لمشروع الفورم المتمركز مع الشرح
===============================================

📄 ملف Form1.cs - الملف الرئيسي:
=================================

using System;
using System.Drawing;
using System.Windows.Forms;

namespace CenterFormProject;

public partial class Form1 : Form
{
    public Form1()
    {
        InitializeComponent(); // تهيئة مكونات النافذة
        
        // تعيين خصائص الفورم الأساسية
        this.Text = "مشروع الفورم المتمركز - Center Form Project"; // عنوان النافذة
        this.Size = new Size(600, 400); // تحديد حجم النافذة (عرض 600، طول 400)
        this.FormBorderStyle = FormBorderStyle.FixedSingle; // منع تغيير الحجم بالسحب
        this.MaximizeBox = false; // إخفاء زر التكبير
        this.MinimizeBox = true; // إبقاء زر التصغير
        
        // تطبيق التمركز عند تحميل الفورم
        CenterFormOnScreen();
        
        // ربط حدث تغيير الحجم بدالة التمركز
        this.Resize += Form1_Resize;
        
        // ربط حدث تحريك الفورم لإعادة تمركزها
        this.LocationChanged += Form1_LocationChanged;
        
        // إضافة عناصر واجهة المستخدم للتوضيح
        AddUIElements();
    }
    
    /// <summary>
    /// دالة لحساب وتطبيق موقع الفورم في منتصف الشاشة
    /// تطبق المعادلة العلمية: (حجم الشاشة - حجم الفورم) ÷ 2
    /// </summary>
    private void CenterFormOnScreen()
    {
        // الحصول على أبعاد الشاشة الأساسية
        int screenWidth = Screen.PrimaryScreen.Bounds.Width;   // عرض الشاشة الكلي
        int screenHeight = Screen.PrimaryScreen.Bounds.Height; // طول الشاشة الكلي
        
        // الحصول على أبعاد الفورم الحالية
        int formWidth = this.Width;   // عرض الفورم
        int formHeight = this.Height; // طول الفورم
        
        // تطبيق المعادلة العلمية لحساب الإحداثيات
        // الإحداثي الأفقي (X): (عرض الشاشة - عرض الفورم) ÷ 2
        int centerX = (screenWidth - formWidth) / 2;
        
        // الإحداثي الرأسي (Y): (طول الشاشة - طول الفورم) ÷ 2
        int centerY = (screenHeight - formHeight) / 2;
        
        // تطبيق الموقع المحسوب على الفورم
        this.Location = new Point(centerX, centerY);
        
        // عرض معلومات الحسابات في شريط العنوان للتوضيح
        this.Text = $"مشروع الفورم المتمركز - الموقع: ({centerX}, {centerY})";
    }
    
    /// <summary>
    /// حدث يتم استدعاؤه عند تغيير حجم الفورم
    /// يضمن بقاء الفورم في المنتصف حتى لو تم تغيير حجمها
    /// </summary>
    private void Form1_Resize(object sender, EventArgs e)
    {
        // إعادة حساب وتطبيق التمركز عند تغيير الحجم
        CenterFormOnScreen();
    }
    
    /// <summary>
    /// حدث يتم استدعاؤه عند تحريك الفورم
    /// يعيد الفورم إلى المنتصف فوراً إذا حاول المستخدم تحريكها
    /// </summary>
    private void Form1_LocationChanged(object sender, EventArgs e)
    {
        // التأكد من أن الفورم مرئية ومحملة بالكامل قبل إعادة التمركز
        if (this.Visible && this.WindowState != FormWindowState.Minimized)
        {
            // إعادة التمركز فوراً
            CenterFormOnScreen();
        }
    }
    
    /// <summary>
    /// دالة لإضافة عناصر واجهة المستخدم للتوضيح والشرح
    /// </summary>
    private void AddUIElements()
    {
        // إنشاء تسمية (Label) لعرض معلومات المشروع
        Label titleLabel = new Label();
        titleLabel.Text = "مشروع الفورم المتمركز"; // النص المعروض
        titleLabel.Font = new Font("Arial", 16, FontStyle.Bold); // نوع وحجم الخط
        titleLabel.ForeColor = Color.DarkBlue; // لون النص
        titleLabel.AutoSize = true; // تحديد الحجم تلقائياً
        titleLabel.Location = new Point(200, 50); // موقع العنصر
        this.Controls.Add(titleLabel); // إضافة العنصر للنافذة
        
        // إنشاء تسمية لعرض شرح المشروع
        Label descriptionLabel = new Label();
        descriptionLabel.Text = "هذا المشروع يضمن بقاء النافذة في منتصف الشاشة دائماً\n" +
                               "حتى لو حاولت تحريكها أو تغيير حجمها";
        descriptionLabel.Font = new Font("Arial", 12);
        descriptionLabel.ForeColor = Color.DarkGreen;
        descriptionLabel.AutoSize = true;
        descriptionLabel.Location = new Point(150, 100);
        this.Controls.Add(descriptionLabel);
        
        // إنشاء تسمية لعرض المعادلة العلمية
        Label formulaLabel = new Label();
        formulaLabel.Text = "المعادلة المستخدمة:\n" +
                           "الموقع الأفقي = (عرض الشاشة - عرض النافذة) ÷ 2\n" +
                           "الموقع الرأسي = (طول الشاشة - طول النافذة) ÷ 2";
        formulaLabel.Font = new Font("Arial", 10);
        formulaLabel.ForeColor = Color.DarkRed;
        formulaLabel.AutoSize = true;
        formulaLabel.Location = new Point(120, 180);
        this.Controls.Add(formulaLabel);
        
        // إنشاء زر لاختبار إعادة التمركز يدوياً
        Button centerButton = new Button();
        centerButton.Text = "إعادة التمركز يدوياً"; // نص الزر
        centerButton.Font = new Font("Arial", 10);
        centerButton.Size = new Size(150, 30); // حجم الزر
        centerButton.Location = new Point(225, 280); // موقع الزر
        centerButton.BackColor = Color.LightBlue; // لون خلفية الزر
        centerButton.Click += (s, e) => CenterFormOnScreen(); // ربط الحدث بالدالة
        this.Controls.Add(centerButton);
        
        // إنشاء تسمية لعرض معلومات الشاشة والفورم
        Label infoLabel = new Label();
        infoLabel.Name = "InfoLabel";
        infoLabel.Font = new Font("Arial", 9);
        infoLabel.ForeColor = Color.Purple;
        infoLabel.AutoSize = true;
        infoLabel.Location = new Point(50, 320);
        UpdateInfoLabel(infoLabel); // تحديث المعلومات
        this.Controls.Add(infoLabel);
    }
    
    /// <summary>
    /// دالة لتحديث معلومات الشاشة والفورم
    /// </summary>
    private void UpdateInfoLabel(Label infoLabel)
    {
        int screenWidth = Screen.PrimaryScreen.Bounds.Width;
        int screenHeight = Screen.PrimaryScreen.Bounds.Height;
        
        infoLabel.Text = $"أبعاد الشاشة: {screenWidth} × {screenHeight}\n" +
                        $"أبعاد النافذة: {this.Width} × {this.Height}\n" +
                        $"موقع النافذة: ({this.Location.X}, {this.Location.Y})";
    }
}

===============================================
📄 ملف Form1.Designer.cs - ملف التصميم:
===============================================

namespace CenterFormProject;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout(); // إيقاف التخطيط مؤقتاً
        // 
        // Form1
        // 
        this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 20F); // أبعاد التحجيم التلقائي
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font; // نمط التحجيم
        this.BackColor = System.Drawing.Color.LightGray; // لون خلفية النافذة
        this.ClientSize = new System.Drawing.Size(600, 400); // حجم منطقة العميل
        this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle; // نوع الحدود
        this.MaximizeBox = false; // إخفاء زر التكبير
        this.Name = "Form1"; // اسم النافذة
        this.StartPosition = System.Windows.Forms.FormStartPosition.Manual; // موقع البداية يدوي
        this.Text = "مشروع الفورم المتمركز"; // عنوان النافذة
        this.ResumeLayout(false); // استئناف التخطيط
    }

    #endregion
}

===============================================
📄 ملف Program.cs - نقطة بداية البرنامج:
===============================================

namespace CenterFormProject;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread] // تحديد نموذج الخيط الواحد
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize(); // تهيئة إعدادات التطبيق
        Application.Run(new Form1()); // تشغيل النافذة الرئيسية
    }
}

===============================================
🔍 شرح المفاهيم البرمجية المستخدمة:
===============================================

1. الأحداث (Events):
   - Resize: يحدث عند تغيير حجم النافذة
   - LocationChanged: يحدث عند تحريك النافذة
   - Click: يحدث عند النقر على الزر

2. خصائص الشاشة:
   - Screen.PrimaryScreen.Bounds.Width: عرض الشاشة الرئيسية
   - Screen.PrimaryScreen.Bounds.Height: ارتفاع الشاشة الرئيسية

3. خصائص النافذة:
   - Width, Height: عرض وارتفاع النافذة
   - Location: موقع النافذة (نقطة X,Y)
   - FormBorderStyle: نوع حدود النافذة
   - MaximizeBox: إظهار/إخفاء زر التكبير

4. عناصر واجهة المستخدم:
   - Label: لعرض النصوص
   - Button: للأزرار التفاعلية
   - Font: لتنسيق الخطوط
   - Color: لتحديد الألوان

5. المعادلة الرياضية:
   - الموقع المركزي = (حجم الشاشة - حجم النافذة) ÷ 2
   - تطبق على كل من المحور الأفقي والرأسي

هذا الكود يضمن بقاء النافذة في منتصف الشاشة دائماً!
