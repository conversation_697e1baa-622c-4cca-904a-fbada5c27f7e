===============================================
شرح مشروع الفورم المتمركز - Center Form Project
===============================================

🎯 الهدف من المشروع:
===================
إنشاء برنامج C# Windows Forms يضمن بقاء نافذة البرنامج (الفورم) في منتصف الشاشة دائماً،
حتى لو حاول المستخدم تحريكها أو تغيير حجمها.

📐 المعادلة العلمية المستخدمة:
=============================
لحساب موقع المنتصف، نستخدم المعادلة التالية:

1. الإحداثي الأفقي (X-axis):
   X = (عرض الشاشة الكلي - عرض النافذة) ÷ 2

2. الإحداثي الرأسي (Y-axis):
   Y = (طول الشاشة الكلي - طول النافذة) ÷ 2

مثال عملي:
- إذا كان عرض الشاشة = 1920 بكسل
- وعرض النافذة = 600 بكسل
- فإن الإحداثي X = (1920 - 600) ÷ 2 = 660 بكسل

🔧 الميزات المطبقة في المشروع:
==============================

1. التمركز التلقائي عند بدء التشغيل
2. إعادة التمركز عند تغيير حجم النافذة
3. إعادة التمركز عند محاولة تحريك النافذة
4. منع تكبير النافذة (MaximizeBox = false)
5. تثبيت حجم النافذة (FormBorderStyle = FixedSingle)
6. عرض معلومات الحسابات في شريط العنوان
7. واجهة مستخدم توضيحية تشرح المشروع

🎮 كيفية استخدام المشروع:
==========================

1. تشغيل البرنامج:
   - افتح مجلد المشروع في موجه الأوامر
   - اكتب الأمر: dotnet run
   - ستظهر النافذة في منتصف الشاشة

2. اختبار الميزات:
   - حاول تحريك النافذة → ستعود للمنتصف فوراً
   - اضغط زر "إعادة التمركز يدوياً" → لإعادة التمركز
   - لاحظ المعلومات المعروضة في النافذة

📁 ملفات المشروع:
==================

1. Form1.cs - الملف الرئيسي الذي يحتوي على منطق التمركز
2. Form1.Designer.cs - ملف تصميم النافذة
3. Program.cs - نقطة بداية البرنامج
4. CenterFormProject.csproj - ملف إعدادات المشروع

🧠 المفاهيم البرمجية المستخدمة:
===============================

1. الأحداث (Events):
   - Resize: يتم استدعاؤه عند تغيير حجم النافذة
   - LocationChanged: يتم استدعاؤه عند تحريك النافذة

2. خصائص الشاشة (Screen Properties):
   - Screen.PrimaryScreen.Bounds.Width: عرض الشاشة
   - Screen.PrimaryScreen.Bounds.Height: طول الشاشة

3. خصائص النافذة (Form Properties):
   - Width, Height: أبعاد النافذة
   - Location: موقع النافذة
   - FormBorderStyle: نوع حدود النافذة

4. عناصر واجهة المستخدم:
   - Label: لعرض النصوص
   - Button: للأزرار التفاعلية

💡 الفوائد التعليمية:
====================

1. فهم نظام الإحداثيات في Windows
2. تطبيق المعادلات الرياضية في البرمجة
3. التعامل مع أحداث النوافذ
4. إنشاء واجهات مستخدم تفاعلية
5. استخدام خصائص الشاشة والنوافذ

🚀 إمكانيات التطوير المستقبلية:
===============================

1. إضافة خيار لتمركز النافذة في شاشات متعددة
2. حفظ واستعادة موقع النافذة المفضل
3. إضافة تأثيرات بصرية عند التمركز
4. إنشاء إعدادات قابلة للتخصيص
5. دعم أحجام نوافذ متغيرة

📞 معلومات إضافية:
===================

هذا المشروع مثال تعليمي ممتاز لفهم:
- كيفية التحكم في موقع النوافذ برمجياً
- استخدام الأحداث للاستجابة لتفاعل المستخدم
- تطبيق المعادلات الرياضية في حل المشاكل البرمجية
- إنشاء تطبيقات Windows Forms احترافية

تم إنشاء هذا المشروع كمثال تعليمي شامل يجمع بين النظرية والتطبيق العملي.
