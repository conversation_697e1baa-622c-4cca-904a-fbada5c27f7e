namespace CenterFormProject;

public partial class Form1 : Form
{
    public Form1()
    {
        InitializeComponent();

        // تعيين خصائص الفورم الأساسية
        this.Text = "مشروع الفورم المتمركز - Center Form Project";
        this.Size = new Size(600, 400); // تحديد حجم الفورم
        this.FormBorderStyle = FormBorderStyle.FixedSingle; // منع تغيير الحجم بالسحب
        this.MaximizeBox = false; // إخفاء زر التكبير
        this.MinimizeBox = true; // إبقاء زر التصغير

        // تطبيق التمركز عند تحميل الفورم
        CenterFormOnScreen();

        // ربط حدث تغيير الحجم بدالة التمركز
        this.Resize += Form1_Resize;

        // ربط حدث تحريك الفورم لإعادة تمركزها
        this.LocationChanged += Form1_LocationChanged;

        // إضافة عناصر واجهة المستخدم للتوضيح
        AddUIElements();
    }

    /// <summary>
    /// دالة لحساب وتطبيق موقع الفورم في منتصف الشاشة
    /// تطبق المعادلة العلمية: (حجم الشاشة - حجم الفورم) ÷ 2
    /// </summary>
    private void CenterFormOnScreen()
    {
        // الحصول على أبعاد الشاشة الأساسية
        int screenWidth = Screen.PrimaryScreen.Bounds.Width;   // عرض الشاشة الكلي
        int screenHeight = Screen.PrimaryScreen.Bounds.Height; // طول الشاشة الكلي

        // الحصول على أبعاد الفورم الحالية
        int formWidth = this.Width;   // عرض الفورم
        int formHeight = this.Height; // طول الفورم

        // تطبيق المعادلة العلمية لحساب الإحداثيات
        // الإحداثي الأفقي (X): (عرض الشاشة - عرض الفورم) ÷ 2
        int centerX = (screenWidth - formWidth) / 2;

        // الإحداثي الرأسي (Y): (طول الشاشة - طول الفورم) ÷ 2
        int centerY = (screenHeight - formHeight) / 2;

        // تطبيق الموقع المحسوب على الفورم
        this.Location = new Point(centerX, centerY);

        // عرض معلومات الحسابات في شريط العنوان للتوضيح
        this.Text = $"مشروع الفورم المتمركز - الموقع: ({centerX}, {centerY})";
    }

    /// <summary>
    /// حدث يتم استدعاؤه عند تغيير حجم الفورم
    /// يضمن بقاء الفورم في المنتصف حتى لو تم تغيير حجمها
    /// </summary>
    private void Form1_Resize(object sender, EventArgs e)
    {
        // إعادة حساب وتطبيق التمركز عند تغيير الحجم
        CenterFormOnScreen();
    }

    /// <summary>
    /// حدث يتم استدعاؤه عند تحريك الفورم
    /// يعيد الفورم إلى المنتصف فوراً إذا حاول المستخدم تحريكها
    /// </summary>
    private void Form1_LocationChanged(object sender, EventArgs e)
    {
        // التأكد من أن الفورم مرئية ومحملة بالكامل قبل إعادة التمركز
        if (this.Visible && this.WindowState != FormWindowState.Minimized)
        {
            // إعادة التمركز فوراً
            CenterFormOnScreen();
        }
    }

    /// <summary>
    /// دالة لإضافة عناصر واجهة المستخدم للتوضيح والشرح
    /// </summary>
    private void AddUIElements()
    {
        // إنشاء تسمية (Label) لعرض معلومات المشروع
        Label titleLabel = new Label();
        titleLabel.Text = "مشروع الفورم المتمركز";
        titleLabel.Font = new Font("Arial", 16, FontStyle.Bold);
        titleLabel.ForeColor = Color.DarkBlue;
        titleLabel.AutoSize = true;
        titleLabel.Location = new Point(200, 50);
        this.Controls.Add(titleLabel);

        // إنشاء تسمية لعرض شرح المشروع
        Label descriptionLabel = new Label();
        descriptionLabel.Text = "هذا المشروع يضمن بقاء النافذة في منتصف الشاشة دائماً\n" +
                               "حتى لو حاولت تحريكها أو تغيير حجمها";
        descriptionLabel.Font = new Font("Arial", 12);
        descriptionLabel.ForeColor = Color.DarkGreen;
        descriptionLabel.AutoSize = true;
        descriptionLabel.Location = new Point(150, 100);
        this.Controls.Add(descriptionLabel);

        // إنشاء تسمية لعرض المعادلة العلمية
        Label formulaLabel = new Label();
        formulaLabel.Text = "المعادلة المستخدمة:\n" +
                           "الموقع الأفقي = (عرض الشاشة - عرض النافذة) ÷ 2\n" +
                           "الموقع الرأسي = (طول الشاشة - طول النافذة) ÷ 2";
        formulaLabel.Font = new Font("Arial", 10);
        formulaLabel.ForeColor = Color.DarkRed;
        formulaLabel.AutoSize = true;
        formulaLabel.Location = new Point(120, 180);
        this.Controls.Add(formulaLabel);

        // إنشاء زر لاختبار إعادة التمركز يدوياً
        Button centerButton = new Button();
        centerButton.Text = "إعادة التمركز يدوياً";
        centerButton.Font = new Font("Arial", 10);
        centerButton.Size = new Size(150, 30);
        centerButton.Location = new Point(225, 280);
        centerButton.BackColor = Color.LightBlue;
        centerButton.Click += (s, e) => CenterFormOnScreen();
        this.Controls.Add(centerButton);

        // إنشاء تسمية لعرض معلومات الشاشة والفورم
        Label infoLabel = new Label();
        infoLabel.Name = "InfoLabel";
        infoLabel.Font = new Font("Arial", 9);
        infoLabel.ForeColor = Color.Purple;
        infoLabel.AutoSize = true;
        infoLabel.Location = new Point(50, 320);
        UpdateInfoLabel(infoLabel);
        this.Controls.Add(infoLabel);
    }

    /// <summary>
    /// دالة لتحديث معلومات الشاشة والفورم
    /// </summary>
    private void UpdateInfoLabel(Label infoLabel)
    {
        int screenWidth = Screen.PrimaryScreen.Bounds.Width;
        int screenHeight = Screen.PrimaryScreen.Bounds.Height;

        infoLabel.Text = $"أبعاد الشاشة: {screenWidth} × {screenHeight}\n" +
                        $"أبعاد النافذة: {this.Width} × {this.Height}\n" +
                        $"موقع النافذة: ({this.Location.X}, {this.Location.Y})";
    }
}
